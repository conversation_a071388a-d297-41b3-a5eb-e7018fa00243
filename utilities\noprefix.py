import re
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any


def duration_to_timedelta(duration_str: str) -> Optional[timedelta]:
    """
    Convert duration string to timedelta object.
    
    Args:
        duration_str: Duration string like "1h", "2d", "1w", "1m", "1y"
    
    Returns:
        timedelta object or None if invalid
    """
    if not duration_str or duration_str.lower() == "lifetime":
        return None
    
    # Parse duration string
    match = re.match(r'^(\d+)([mhdwMy])$', duration_str.lower())
    if not match:
        return None
    
    amount, unit = match.groups()
    amount = int(amount)
    
    if unit == 'm':  # minutes
        return timedelta(minutes=amount)
    elif unit == 'h':  # hours
        return timedelta(hours=amount)
    elif unit == 'd':  # days
        return timedelta(days=amount)
    elif unit == 'w':  # weeks
        return timedelta(weeks=amount)
    elif unit == 'M':  # months (approximate)
        return timedelta(days=amount * 30)
    elif unit == 'y':  # years (approximate)
        return timedelta(days=amount * 365)
    
    return None


def format_expiry_time(expiry_time: Optional[datetime]) -> tuple[str, Optional[str]]:
    """
    Format expiry time for display.
    
    Args:
        expiry_time: Expiry datetime or None for lifetime
    
    Returns:
        Tuple of (display_string, timestamp_string)
    """
    if expiry_time is None:
        return "Never (Lifetime)", None
    
    timestamp = int(expiry_time.timestamp())
    display = f"<t:{timestamp}:R>"
    timestamp_str = f"<t:{timestamp}:F>"
    
    return display, timestamp_str


class NoPrefixManager:
    """Manager class for no-prefix functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self._cache = {}  # user_id -> expiry_time cache
    
    async def initialize(self):
        """Initialize the no-prefix manager and load cache"""
        await self._load_cache()
    
    async def _load_cache(self):
        """Load all no-prefix users into cache"""
        query = """
                SELECT user_id, expiry_time
                FROM noprefix_users
                WHERE expiry_time IS NULL OR expiry_time > NOW()
                """
        records = await self.bot.cxn.fetch(query)
        
        self._cache = {}
        for record in records:
            self._cache[record['user_id']] = record['expiry_time']
    
    async def is_user_noprefix(self, user_id: int) -> bool:
        """Check if a user has no-prefix permissions"""
        if user_id not in self._cache:
            return False
        
        expiry_time = self._cache[user_id]
        if expiry_time is None:  # Lifetime
            return True
        
        # Check if expired
        if expiry_time <= datetime.utcnow():
            # Remove from cache and database
            await self.remove_user(user_id)
            return False
        
        return True
    
    async def add_user(self, user_id: int, expiry_time: Optional[datetime], added_by: int) -> bool:
        """
        Add a user to no-prefix list.
        
        Args:
            user_id: User ID to add
            expiry_time: When the no-prefix expires (None for lifetime)
            added_by: User ID who added this user
        
        Returns:
            True if successful, False otherwise
        """
        try:
            query = """
                    INSERT INTO noprefix_users (user_id, expiry_time, added_by)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (user_id) DO UPDATE SET
                        expiry_time = $2,
                        added_by = $3,
                        added_at = NOW()
                    """
            await self.bot.cxn.execute(query, user_id, expiry_time, added_by)
            
            # Update cache
            self._cache[user_id] = expiry_time
            return True
        except Exception as e:
            print(f"[NoPrefix] Error adding user {user_id}: {e}")
            return False
    
    async def remove_user(self, user_id: int) -> bool:
        """
        Remove a user from no-prefix list.
        
        Args:
            user_id: User ID to remove
        
        Returns:
            True if user was removed, False if not found
        """
        try:
            query = "DELETE FROM noprefix_users WHERE user_id = $1"
            result = await self.bot.cxn.execute(query, user_id)
            
            # Remove from cache
            if user_id in self._cache:
                del self._cache[user_id]
            
            # Check if any rows were affected
            return "DELETE 1" in result
        except Exception as e:
            print(f"[NoPrefix] Error removing user {user_id}: {e}")
            return False
    
    async def get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed information about a no-prefix user"""
        query = """
                SELECT user_id, expiry_time, added_by, added_at
                FROM noprefix_users
                WHERE user_id = $1
                """
        record = await self.bot.cxn.fetchrow(query, user_id)
        
        if not record:
            return None
        
        return dict(record)
    
    async def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all no-prefix users"""
        query = """
                SELECT user_id, expiry_time, added_by, added_at
                FROM noprefix_users
                ORDER BY added_at DESC
                """
        records = await self.bot.cxn.fetch(query)
        return [dict(record) for record in records]
    
    async def cleanup_expired(self) -> List[int]:
        """Remove expired no-prefix users and return their IDs"""
        query = """
                DELETE FROM noprefix_users
                WHERE expiry_time IS NOT NULL AND expiry_time <= NOW()
                RETURNING user_id
                """
        records = await self.bot.cxn.fetch(query)
        expired_users = [record['user_id'] for record in records]
        
        # Remove from cache
        for user_id in expired_users:
            if user_id in self._cache:
                del self._cache[user_id]
        
        return expired_users
